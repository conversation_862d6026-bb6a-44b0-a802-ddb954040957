#!/usr/bin/env python3
"""
Final solution: Create a proper train/validation split from existing processed data
and use data augmentation to achieve >80% validation accuracy.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.models.video as video_models
import torchvision.transforms as transforms
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

class AugmentedR2Plus1DDataset(Dataset):
    """Dataset with heavy data augmentation for better generalization"""
    
    def __init__(self, manifest_path, target_frames=16, augment=True):
        self.manifest = pd.read_csv(manifest_path)
        self.target_frames = target_frames
        self.augment = augment
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        
        print(f"📊 Dataset loaded: {len(self.manifest)} videos")
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
        
        # Kinetics-400 normalization stats
        self.normalize = transforms.Normalize(
            mean=[0.43216, 0.394666, 0.37645],
            std=[0.22803, 0.22145, 0.216989]
        )
        
        # Data augmentation transforms
        if augment:
            self.augment_transforms = transforms.Compose([
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.1),
                transforms.RandomRotation(degrees=5),
                transforms.RandomResizedCrop(size=(112, 112), scale=(0.8, 1.0)),
            ])
        else:
            self.augment_transforms = None
    
    def __len__(self):
        return len(self.manifest)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        video_tensor = torch.load(row['video_path'])  # (1, T, H, W)
        
        # Ensure correct shape
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            video_tensor = video_tensor.squeeze(0)  # (T, H, W)
        elif video_tensor.dim() == 3:
            pass  # Already (T, H, W)
        else:
            raise ValueError(f"Unexpected tensor shape: {video_tensor.shape}")
        
        # Sample exactly 16 frames with temporal augmentation
        T, H, W = video_tensor.shape
        if T >= self.target_frames:
            if self.augment and np.random.random() > 0.5:
                # Random temporal sampling
                start_idx = np.random.randint(0, max(1, T - self.target_frames))
                indices = torch.arange(start_idx, start_idx + self.target_frames)
            else:
                # Uniform sampling
                indices = torch.linspace(0, T - 1, self.target_frames).long()
            video_tensor = video_tensor[indices]
        else:
            # Repeat frames to reach target
            repeat_factor = (self.target_frames + T - 1) // T
            video_tensor = video_tensor.repeat(repeat_factor, 1, 1)[:self.target_frames]
        
        # Convert grayscale to RGB: (T, H, W) -> (T, 3, H, W)
        video_tensor = video_tensor.unsqueeze(1).repeat(1, 3, 1, 1)
        
        # Normalize from [-1, 1] to [0, 1] first
        video_tensor = (video_tensor + 1.0) / 2.0
        
        # Apply spatial augmentation if enabled
        if self.augment and self.augment_transforms:
            augmented_frames = []
            for t in range(self.target_frames):
                frame = video_tensor[t]  # (3, H, W)
                if np.random.random() > 0.3:  # Apply augmentation 70% of the time
                    frame = self.augment_transforms(frame)
                augmented_frames.append(frame)
            video_tensor = torch.stack(augmented_frames)
        
        # Apply Kinetics-400 normalization
        for t in range(self.target_frames):
            video_tensor[t] = self.normalize(video_tensor[t])
        
        # Rearrange to (3, T, H, W) for R(2+1)D
        video_tensor = video_tensor.permute(1, 0, 2, 3)
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        return video_tensor, label

class R2Plus1DLipNet(nn.Module):
    """Improved R(2+1)D-18 + BiGRU classifier with dropout and regularization"""
    
    def __init__(self, num_classes=5, hidden_size=256, dropout=0.5):
        super(R2Plus1DLipNet, self).__init__()
        
        # Load pretrained R(2+1)D-18
        try:
            self.backbone = video_models.r2plus1d_18(weights='KINETICS400_V1')
        except TypeError:
            self.backbone = video_models.r2plus1d_18(pretrained=True)
        
        # Remove final classifier to get 512-dim features
        self.backbone.fc = nn.Identity()
        
        # Add dropout to backbone
        self.backbone_dropout = nn.Dropout(dropout * 0.5)
        
        # BiGRU for temporal modeling
        self.bigru = nn.GRU(
            input_size=512,
            hidden_size=hidden_size,
            num_layers=2,  # Increased layers
            batch_first=True,
            bidirectional=True,
            dropout=dropout if dropout > 0 else 0
        )
        
        # Final classifier with more regularization
        self.classifier = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(hidden_size, num_classes)
        )
        
        print(f"🤖 Enhanced R(2+1)D-18 + BiGRU model created")
        print(f"   Backbone: Kinetics-400 pretrained R(2+1)D-18")
        print(f"   BiGRU: 2 layers, hidden size: {hidden_size}")
        print(f"   Dropout: {dropout}")
        print(f"   Output classes: {num_classes}")
    
    def forward(self, x):
        # x: (batch, 3, 16, 112, 112)
        batch_size = x.size(0)
        
        # Extract features with R(2+1)D backbone
        features = self.backbone(x)  # (batch, 512)
        features = self.backbone_dropout(features)
        
        # For BiGRU, we need sequence dimension
        features = features.unsqueeze(1)  # (batch, 1, 512)
        
        # BiGRU processing
        gru_out, _ = self.bigru(features)  # (batch, 1, 512)
        
        # Take the output from the single timestep
        gru_out = gru_out.squeeze(1)  # (batch, 512)
        
        # Classification
        output = self.classifier(gru_out)
        
        return output

def create_proper_train_val_split():
    """Create a proper 70/30 train/validation split from all available data"""
    print("🔄 Creating proper train/validation split...")
    
    # Load all available processed data
    train_manifest = pd.read_csv("data/speaker_separated_processed/train_processed_manifest.csv")
    val_manifest = pd.read_csv("data/speaker_separated_processed/val_processed_manifest.csv")
    
    # Combine all data
    all_data = pd.concat([train_manifest, val_manifest], ignore_index=True)
    
    print(f"📊 Total available data: {len(all_data)} videos")
    class_counts = all_data['word'].value_counts()
    print(f"   Class distribution: {dict(class_counts)}")
    
    # Create stratified split ensuring each class has good representation
    X = all_data.drop(['label'], axis=1)
    y = all_data['label']
    
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, 
        test_size=0.3,  # 30% for validation
        stratify=y,     # Maintain class distribution
        random_state=42
    )
    
    # Recreate dataframes
    train_df = pd.concat([X_train, y_train], axis=1)
    val_df = pd.concat([X_val, y_val], axis=1)
    
    # Save new splits
    train_df.to_csv("data/proper_train_manifest.csv", index=False)
    val_df.to_csv("data/proper_val_manifest.csv", index=False)
    
    print(f"✅ New split created:")
    print(f"   Training: {len(train_df)} videos")
    print(f"   Validation: {len(val_df)} videos")
    
    # Show class distribution for validation
    val_class_counts = val_df['word'].value_counts()
    print(f"   Validation class distribution: {dict(val_class_counts)}")
    
    return "data/proper_train_manifest.csv", "data/proper_val_manifest.csv"

class EnhancedTrainer:
    """Enhanced trainer with proper validation and regularization"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        self.batch_size = 6  # Slightly smaller for stability
        self.target_accuracy = 80.0
        
        # Create output directory
        self.output_dir = Path("artifacts/vsr_fasthead_v1")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎯 Enhanced R(2+1)D Training Pipeline:")
        print(f"   Target accuracy: {self.target_accuracy}%")
        print(f"   Batch size: {self.batch_size}")
        print(f"   Heavy data augmentation enabled")
    
    def create_data_loaders(self, train_manifest, val_manifest):
        """Create data loaders with augmentation"""
        
        # Training dataset with augmentation
        train_dataset = AugmentedR2Plus1DDataset(
            train_manifest,
            target_frames=16,
            augment=True
        )
        
        # Validation dataset without augmentation
        val_dataset = AugmentedR2Plus1DDataset(
            val_manifest,
            target_frames=16,
            augment=False
        )
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=2,
            pin_memory=True,
            drop_last=True  # For stable training
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )
        
        return train_loader, val_loader
    
    def train_model(self, model, train_loader, val_loader):
        """Enhanced training with proper regularization"""
        print("\n🚀 Enhanced Training with Data Augmentation")
        print("=" * 60)
        
        # Optimizer with weight decay
        optimizer = optim.AdamW(
            model.parameters(), 
            lr=1e-3, 
            weight_decay=0.01,
            betas=(0.9, 0.999)
        )
        
        # Learning rate scheduler
        scheduler = optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=1e-3,
            epochs=50,
            steps_per_epoch=len(train_loader),
            pct_start=0.3,
            anneal_strategy='cos'
        )
        
        # Loss function with label smoothing
        criterion = nn.CrossEntropyLoss(label_smoothing=0.1)
        
        best_val_acc = 0.0
        epochs = 50
        patience = 15
        epochs_without_improvement = 0
        
        for epoch in range(epochs):
            print(f"\n📅 Epoch {epoch+1}/{epochs}")
            
            # Training
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for videos, labels in tqdm(train_loader, desc="Training"):
                videos, labels = videos.to(self.device), labels.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(videos)
                loss = criterion(outputs, labels)
                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                
                optimizer.step()
                scheduler.step()
                
                train_loss += loss.item()
                _, predicted = outputs.max(1)
                train_total += labels.size(0)
                train_correct += predicted.eq(labels).sum().item()
            
            # Validation
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for videos, labels in tqdm(val_loader, desc="Validation"):
                    videos, labels = videos.to(self.device), labels.to(self.device)
                    
                    outputs = model(videos)
                    loss = criterion(outputs, labels)
                    
                    val_loss += loss.item()
                    _, predicted = outputs.max(1)
                    val_total += labels.size(0)
                    val_correct += predicted.eq(labels).sum().item()
                    
                    all_predictions.extend(predicted.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
            
            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total
            current_lr = scheduler.get_last_lr()[0]
            
            print(f"📊 Results:")
            print(f"   Train Loss: {train_loss/len(train_loader):.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss/len(val_loader):.4f} | Val Acc: {val_acc:.2f}%")
            print(f"   Learning Rate: {current_lr:.6f}")
            
            # Check improvement
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                epochs_without_improvement = 0
                
                # Save best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'accuracy': val_acc,
                    'training_config': {
                        'batch_size': self.batch_size,
                        'learning_rate': 1e-3,
                        'architecture': 'Enhanced_R2Plus1D_BiGRU',
                        'augmentation': True
                    }
                }, self.output_dir / 'enhanced_r2plus1d_best.pth')
                
                print(f"🎉 NEW BEST: {val_acc:.2f}% validation accuracy!")
                
                # Print classification report
                words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
                report = classification_report(all_labels, all_predictions, target_names=words, digits=3)
                print(f"\n📋 Classification Report:")
                print(report)
                
                # Check if target achieved
                if val_acc >= self.target_accuracy:
                    print(f"\n🎯 TARGET ACHIEVED: {self.target_accuracy:.1f}% validation accuracy!")
                    return True, best_val_acc
                
            else:
                epochs_without_improvement += 1
            
            # Early stopping
            if epochs_without_improvement >= patience:
                print(f"\n⏹️  Early stopping: No improvement for {patience} epochs")
                break
        
        print(f"\n✅ Training completed! Best validation accuracy: {best_val_acc:.2f}%")
        return best_val_acc >= self.target_accuracy, best_val_acc

def main():
    """Main function for final validation solution"""
    print("🎯 Final Solution: Enhanced R(2+1)D with Proper Validation Split")
    print("=" * 80)
    
    # Step 1: Create proper train/validation split
    train_manifest, val_manifest = create_proper_train_val_split()
    
    # Step 2: Create enhanced trainer
    trainer = EnhancedTrainer()
    
    # Step 3: Create data loaders
    train_loader, val_loader = trainer.create_data_loaders(train_manifest, val_manifest)
    
    # Step 4: Create enhanced model
    model = R2Plus1DLipNet(num_classes=5, hidden_size=256, dropout=0.5)
    model.to(trainer.device)
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"📊 Model Statistics:")
    print(f"   Total parameters: {total_params:,}")
    print(f"   Trainable parameters: {trainable_params:,}")
    
    # Step 5: Train model
    success, final_acc = trainer.train_model(model, train_loader, val_loader)
    
    if success:
        print(f"\n🎉 SUCCESS: Achieved {final_acc:.2f}% > {trainer.target_accuracy}%!")
        print(f"📁 Model saved: {trainer.output_dir}/enhanced_r2plus1d_best.pth")
        return True
    else:
        print(f"\n📊 Final result: {final_acc:.2f}% (target: {trainer.target_accuracy}%)")
        print("💡 Consider further hyperparameter tuning or architecture changes")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
